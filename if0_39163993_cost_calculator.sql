-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version *******
-- https://www.phpmyadmin.net/
--
-- Host: sql307.infinityfree.com
-- Generation Time: Jun 05, 2025 at 08:31 AM
-- Server version: 10.6.19-MariaDB
-- PHP Version: 7.2.22

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `if0_39163993_cost_calculator`
--

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`id`, `name`, `description`, `created_at`) VALUES
(11, 'Operating Expenses', '', '2025-06-05 09:07:00'),
(12, 'Test', '', '2025-06-05 09:49:25');

-- --------------------------------------------------------

--
-- Table structure for table `costs`
--

CREATE TABLE `costs` (
  `id` int(11) NOT NULL,
  `description` varchar(255) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `category_id` int(11) NOT NULL,
  `subcategory_id` int(11) NOT NULL,
  `rate_type` enum('daily','monthly') NOT NULL DEFAULT 'daily',
  `num_days` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `costs`
--

INSERT INTO `costs` (`id`, `description`, `amount`, `category_id`, `subcategory_id`, `rate_type`, `num_days`, `created_at`) VALUES
(12, 'Management Fees', '14664.00', 11, 18, 'daily', 1, '2025-06-05 09:13:35'),
(13, 'Audit Fees', '506.00', 11, 19, 'daily', 1, '2025-06-05 09:13:53'),
(14, 'Bank Charges', '83.00', 11, 20, 'daily', 1, '2025-06-05 09:14:12'),
(15, 'Building Hygiene', '149.00', 11, 21, 'daily', 1, '2025-06-05 09:14:38'),
(17, 'Consulting fees payroll', '250.00', 11, 22, 'daily', 1, '2025-06-05 09:18:23'),
(18, 'Consulting fees companies', '1133.00', 11, 23, 'daily', 1, '2025-06-05 09:18:38'),
(19, 'Amortisation of Goodwill', '622.00', 11, 24, 'daily', 1, '2025-06-05 09:18:59'),
(20, 'EC - Salary Costs', '40251.00', 11, 25, 'daily', 1, '2025-06-05 09:19:19'),
(21, 'EC - UIF', '222.00', 11, 26, 'daily', 1, '2025-06-05 09:19:46'),
(22, 'EC - Group Benefits', '2411.00', 11, 27, 'daily', 1, '2025-06-05 09:20:03'),
(23, 'EC - Staff Welfare', '165.00', 11, 28, 'daily', 1, '2025-06-05 09:20:18'),
(24, 'Insurance', '564.00', 11, 29, 'daily', 1, '2025-06-05 09:20:38'),
(25, 'IT - Computer Consumables', '37.00', 11, 30, 'daily', 1, '2025-06-05 09:20:58'),
(26, 'IT - Back up Facilities', '408.00', 11, 31, 'daily', 1, '2025-06-05 09:21:17'),
(27, 'IT - Non Perpetual Licencing', '4218.00', 11, 32, 'daily', 1, '2025-06-05 09:21:46'),
(28, 'Legal Fees', '256.00', 11, 33, 'daily', 1, '2025-06-05 09:22:06'),
(29, 'Licences - External Databases', '3307.00', 11, 34, 'daily', 1, '2025-06-05 09:22:57'),
(30, 'Levies - Skills Development Levy', '403.00', 11, 35, 'daily', 1, '2025-06-05 09:23:17'),
(31, 'Marketing - BU Specific', '173.00', 11, 36, 'daily', 1, '2025-06-05 09:23:38'),
(32, 'MV Expenses', '396.00', 11, 37, 'daily', 1, '2025-06-05 09:24:05'),
(33, 'Printing', '227.00', 11, 38, 'daily', 1, '2025-06-05 09:24:27'),
(34, 'Rental - Premises', '416.00', 11, 39, 'daily', 1, '2025-06-05 09:24:46'),
(35, 'Repairs & Maintenance', '266.00', 11, 40, 'daily', 1, '2025-06-05 09:25:15'),
(36, 'Stationery & Subs', '564.00', 11, 41, 'daily', 1, '2025-06-05 09:25:35'),
(37, 'Travel - Local', '269.00', 11, 42, 'daily', 1, '2025-06-05 09:25:54'),
(38, 'Recharges - BEE', '2712.00', 11, 43, 'daily', 1, '2025-06-05 09:26:11'),
(39, 'Recharges - Finance', '3370.00', 11, 44, 'daily', 1, '2025-06-05 09:26:28'),
(40, 'Recharges - MI', '567.00', 11, 45, 'daily', 1, '2025-06-05 09:26:54'),
(41, 'Recharges - HRbD', '3574.00', 11, 46, 'daily', 1, '2025-06-05 09:27:15'),
(42, 'Recharges - Support JHB', '1577.00', 11, 47, 'daily', 1, '2025-06-05 09:27:49'),
(43, 'Recharges - Support CPT', '123.00', 11, 48, 'daily', 1, '2025-06-05 09:28:09'),
(44, 'Recharges - TS Support', '4543.00', 11, 49, 'daily', 1, '2025-06-05 09:28:26'),
(45, 'Recharges - TS Depreciaiton', '79.00', 11, 50, 'daily', 1, '2025-06-05 09:28:51'),
(46, 'test', '12.00', 12, 51, 'daily', 1, '2025-06-05 09:50:06');

-- --------------------------------------------------------

--
-- Table structure for table `invoices`
--

CREATE TABLE `invoices` (
  `id` int(11) NOT NULL,
  `student_name` varchar(100) NOT NULL,
  `course` varchar(100) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `tax` decimal(5,2) NOT NULL DEFAULT 0.00,
  `due_date` date NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `revenue`
--

CREATE TABLE `revenue` (
  `id` int(11) NOT NULL,
  `month_year` varchar(7) NOT NULL,
  `student_type` varchar(50) NOT NULL,
  `revenue_per_student` decimal(10,2) NOT NULL,
  `total_students` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `subcategories`
--

CREATE TABLE `subcategories` (
  `id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `subcategories`
--

INSERT INTO `subcategories` (`id`, `category_id`, `name`, `description`, `created_at`) VALUES
(18, 11, 'Management Fees', '', '2025-06-05 09:07:16'),
(19, 11, 'Audit Fees', '', '2025-06-05 09:07:27'),
(20, 11, 'Bank Charges', '', '2025-06-05 09:07:42'),
(21, 11, 'Building Hygiene', '', '2025-06-05 09:08:07'),
(22, 11, 'Consulting Fees Payroll', '', '2025-06-05 09:09:01'),
(23, 11, 'Consulting Fees Companies', '', '2025-06-05 09:09:16'),
(24, 11, 'Amortisation of Goodwill', '', '2025-06-05 09:09:26'),
(25, 11, 'EC - Salary Costs', '', '2025-06-05 09:09:35'),
(26, 11, 'EC - UIF', '', '2025-06-05 09:09:42'),
(27, 11, 'EC - Group Benefits', '', '2025-06-05 09:09:49'),
(28, 11, 'EC - Staff Welfare', '', '2025-06-05 09:09:56'),
(29, 11, 'Insurance', '', '2025-06-05 09:10:02'),
(30, 11, 'IT - Computer Consumables', '', '2025-06-05 09:10:08'),
(31, 11, 'IT - Back up Facilities', '', '2025-06-05 09:10:13'),
(32, 11, 'IT - Non Perpetual Licencing', '', '2025-06-05 09:10:18'),
(33, 11, 'Legal Fees', '', '2025-06-05 09:10:23'),
(34, 11, 'Licences - External Databases', '', '2025-06-05 09:10:45'),
(35, 11, 'Levies - Skills Development Levy', '', '2025-06-05 09:10:51'),
(36, 11, 'Marketing - BU Specific', '', '2025-06-05 09:10:57'),
(37, 11, 'MV Expenses', '', '2025-06-05 09:11:02'),
(38, 11, 'Printing', '', '2025-06-05 09:11:09'),
(39, 11, 'Rental - Premises', '', '2025-06-05 09:11:14'),
(40, 11, 'Repairs & Maintenance', '', '2025-06-05 09:11:21'),
(41, 11, 'Stationery & Subs', '', '2025-06-05 09:11:29'),
(42, 11, 'Travel - Local', '', '2025-06-05 09:11:35'),
(43, 11, 'Recharges - BEE', '', '2025-06-05 09:11:39'),
(44, 11, 'Recharges - Finance', '', '2025-06-05 09:11:44'),
(45, 11, 'Recharges - MI', '', '2025-06-05 09:11:50'),
(46, 11, 'Recharges - HRbD', '', '2025-06-05 09:11:55'),
(47, 11, 'Recharges - Support JHB', '', '2025-06-05 09:12:02'),
(48, 11, 'Recharges - Support CPT', '', '2025-06-05 09:12:07'),
(49, 11, 'Recharges - TS Support', '', '2025-06-05 09:12:12'),
(50, 11, 'Recharges - TS Depreciaiton', '', '2025-06-05 09:12:17'),
(51, 12, 'Test', '', '2025-06-05 09:49:49');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','viewer') NOT NULL DEFAULT 'viewer',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `password`, `role`, `created_at`) VALUES
(1, 'admin', '$2y$10$xkIlFSooNnNre2uQFByCbuqkfeGejg2eMuc7QO.HV6ZjY8EoVYGsu', 'admin', '2025-05-24 19:15:51'),
(2, 'rhea', '$2y$10$gJpOlXmz8dogN0fxh.Y.geBbehhWpBHj1xerYp7ok46QjJR6YuHdG', 'viewer', '2025-05-28 13:20:32'),
(3, 'Ashish', '$2y$10$zuP7ZjqnlTks5DJv3hIAn.taMnVDqkBBtnW1Xg7AvLcmjhS85kJGO', 'viewer', '2025-05-28 13:30:03'),
(4, 'nav', '$2y$10$8lYJtq9twlw5TCkDPrN0euwk7KiltdeO61DD.PLXDyTk/3Lwf0hG6', 'viewer', '2025-06-02 07:18:07');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `costs`
--
ALTER TABLE `costs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `category_id` (`category_id`),
  ADD KEY `subcategory_id` (`subcategory_id`);

--
-- Indexes for table `invoices`
--
ALTER TABLE `invoices`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `revenue`
--
ALTER TABLE `revenue`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `subcategories`
--
ALTER TABLE `subcategories`
  ADD PRIMARY KEY (`id`),
  ADD KEY `category_id` (`category_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `costs`
--
ALTER TABLE `costs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=47;

--
-- AUTO_INCREMENT for table `invoices`
--
ALTER TABLE `invoices`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `revenue`
--
ALTER TABLE `revenue`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `subcategories`
--
ALTER TABLE `subcategories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=52;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `costs`
--
ALTER TABLE `costs`
  ADD CONSTRAINT `costs_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`),
  ADD CONSTRAINT `costs_ibfk_2` FOREIGN KEY (`subcategory_id`) REFERENCES `subcategories` (`id`);

--
-- Constraints for table `subcategories`
--
ALTER TABLE `subcategories`
  ADD CONSTRAINT `subcategories_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
