<?php
echo "<h1>InfinityFree Diagnostic Tool</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
.success { color: green; }
.error { color: red; }
.warning { color: orange; }
</style>";

echo "<div class='section'>";
echo "<h2>1. Server Information</h2>";
echo "<p><strong>Server:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</p>";
echo "<p><strong>Script Path:</strong> " . __FILE__ . "</p>";
echo "<p><strong>Current Directory:</strong> " . getcwd() . "</p>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>2. File Structure Check</h2>";

$files_to_check = [
    'index.php',
    'setup.php',
    'config/db.php',
    'assets/css/style.css',
    'auth/login.php',
    'admin/dashboard.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo "<p class='success'>✓ $file exists ($size bytes)</p>";
    } else {
        echo "<p class='error'>✗ $file missing</p>";
    }
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>3. CSS File Check</h2>";
if (file_exists('assets/css/style.css')) {
    $css_content = file_get_contents('assets/css/style.css');
    $css_size = strlen($css_content);
    echo "<p class='success'>✓ CSS file exists ($css_size characters)</p>";
    echo "<p><strong>First 200 characters:</strong></p>";
    echo "<pre>" . htmlspecialchars(substr($css_content, 0, 200)) . "...</pre>";
} else {
    echo "<p class='error'>✗ CSS file not found</p>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>4. Database Connection Test</h2>";
$host = 'sql307.infinityfree.com';
$user = 'if0_39163993';
$pass = 'sV5RSK88Jk5';
$db = 'if0_39163993_cost_calculator';

try {
    $mysqli = new mysqli($host, $user, $pass, $db);
    if ($mysqli->connect_error) {
        echo "<p class='error'>✗ Database connection failed: " . $mysqli->connect_error . "</p>";
    } else {
        echo "<p class='success'>✓ Database connection successful</p>";
        
        // Check if tables exist
        $tables = ['users', 'categories', 'subcategories', 'costs', 'revenue', 'invoices'];
        foreach ($tables as $table) {
            $result = $mysqli->query("SHOW TABLES LIKE '$table'");
            if ($result && $result->num_rows > 0) {
                echo "<p class='success'>✓ Table '$table' exists</p>";
            } else {
                echo "<p class='warning'>⚠ Table '$table' missing</p>";
            }
        }
        $mysqli->close();
    }
} catch (Exception $e) {
    echo "<p class='error'>✗ Database error: " . $e->getMessage() . "</p>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>5. URL Testing</h2>";
$base_url = 'http' . (isset($_SERVER['HTTPS']) ? 's' : '') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
echo "<p><strong>Base URL:</strong> $base_url</p>";
echo "<p><strong>CSS URL:</strong> <a href='$base_url/assets/css/style.css' target='_blank'>$base_url/assets/css/style.css</a></p>";
echo "<p>Click the CSS URL above to test if it loads correctly.</p>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>6. Directory Permissions</h2>";
$dirs_to_check = ['.', 'assets', 'assets/css', 'config', 'auth', 'admin'];
foreach ($dirs_to_check as $dir) {
    if (is_dir($dir)) {
        $perms = substr(sprintf('%o', fileperms($dir)), -4);
        echo "<p>Directory '$dir': $perms</p>";
    }
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>7. Recent Changes Check</h2>";
echo "<p><strong>This diagnostic was run at:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p>If you uploaded files recently, check the timestamps:</p>";
foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $mtime = filemtime($file);
        echo "<p>$file: " . date('Y-m-d H:i:s', $mtime) . "</p>";
    }
}
echo "</div>";
?>
