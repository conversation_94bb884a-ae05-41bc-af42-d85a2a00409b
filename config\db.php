<?php
// /config/db.php
$host = 'sql307.infinityfree.com';     // InfinityFree MySQL Hostname
$db   = 'if0_39163993_cost_calculator'; // InfinityFree Database Name
$user = 'if0_39163993';                // InfinityFree MySQL Username
$pass = 'sV5RSK88Jk5';                 // InfinityFree MySQL Password
$charset = 'utf8mb4';

// Check if any MySQL extensions are available
if (!extension_loaded('pdo_mysql') && !extension_loaded('mysqli')) {
    // Neither PDO nor mysqli is available - display helpful error message
    echo '<div style="background-color: #f8d7da; color: #721c24; padding: 20px; margin: 20px; border-radius: 5px; border: 1px solid #f5c6cb;">';
    echo '<h2>Database Connection Error</h2>';
    echo '<p>Your PHP installation is missing required MySQL extensions.</p>';
    echo '<h3>How to fix this:</h3>';
    echo '<ol>';
    echo '<li><strong>Enable PHP Extensions:</strong> You need to enable either the <code>pdo_mysql</code> or <code>mysqli</code> extension in your PHP configuration.</li>';
    echo '<li><strong>Edit php.ini:</strong>';
    echo '<ul>';
    echo '<li>Find your php.ini file (usually in C:\xampp\php\ or C:\wamp64\bin\php\php[version]\)</li>';
    echo '<li>Uncomment these lines by removing the semicolon (;) at the beginning:';
    echo '<pre>';
    echo ';extension=mysqli<br>';
    echo ';extension=pdo_mysql';
    echo '</pre></li>';
    echo '<li>Save the file and restart your web server</li>';
    echo '</ul></li>';
    echo '<li><strong>Alternative:</strong> If you cannot modify php.ini, you may need to install a complete web server package like XAMPP or WAMP.</li>';
    echo '</ol>';
    echo '<p>Once you\'ve enabled the extensions, refresh this page.</p>';
    echo '</div>';
    exit;
}

// Try to use PDO if available
if (extension_loaded('pdo_mysql')) {
    try {
        $dsn = "mysql:host=$host;dbname=$db;charset=$charset";
        $options = [
            PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES   => false,
        ];

        $pdo = new PDO($dsn, $user, $pass, $options);
    } catch (PDOException $e) {
        // Check if it's a "database doesn't exist" error
        if (strpos($e->getMessage(), "Unknown database") !== false) {
            // Try to create the database
            try {
                $rootDsn = "mysql:host=$host;charset=$charset";
                $rootPdo = new PDO($rootDsn, $user, $pass, $options);

                // Create the database
                $rootPdo->exec("CREATE DATABASE IF NOT EXISTS `$db`");

                // Try connecting again
                $pdo = new PDO($dsn, $user, $pass, $options);

                // Redirect to setup script
                header("Location: ../setup.php");
                exit;
            } catch (PDOException $e2) {
                // If we still can't connect, show error
                echo '<div style="background-color: #f8d7da; color: #721c24; padding: 20px; margin: 20px; border-radius: 5px; border: 1px solid #f5c6cb;">';
                echo '<h2>Database Connection Error</h2>';
                echo '<p>Could not create or connect to the database.</p>';
                echo '<p>Error: ' . htmlspecialchars($e2->getMessage()) . '</p>';
                echo '<p>Please run the <a href="/cost calculator may/setup.php">setup script</a> manually.</p>';
                echo '</div>';
                exit;
            }
        } else {
            // Other PDO connection error
            echo '<div style="background-color: #f8d7da; color: #721c24; padding: 20px; margin: 20px; border-radius: 5px; border: 1px solid #f5c6cb;">';
            echo '<h2>Database Connection Error</h2>';
            echo '<p>Could not connect to the database using PDO.</p>';
            echo '<p>Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
            echo '<p>Please check your database settings and make sure MySQL is running.</p>';
            echo '</div>';
            exit;
        }
    }
}
// If PDO is not available but mysqli is, use that
elseif (extension_loaded('mysqli')) {
    // Create connection using mysqli
    try {
        $mysqli = new mysqli($host, $user, $pass, $db);

        // Check connection
        if ($mysqli->connect_error) {
            // Check if it's a "database doesn't exist" error
            if (strpos($mysqli->connect_error, "Unknown database") !== false) {
                // Try to create the database
                $rootMysqli = new mysqli($host, $user, $pass);
                if ($rootMysqli->connect_error) {
                    throw new Exception("Could not connect to MySQL: " . $rootMysqli->connect_error);
                }

                // Create the database
                $rootMysqli->query("CREATE DATABASE IF NOT EXISTS `$db`");
                $rootMysqli->close();

                // Try connecting again
                $mysqli = new mysqli($host, $user, $pass, $db);
                if ($mysqli->connect_error) {
                    throw new Exception("Could not connect to the new database: " . $mysqli->connect_error);
                }

                // Redirect to setup script
                header("Location: ../setup.php");
                exit;
            } else {
                throw new Exception("Database connection failed: " . $mysqli->connect_error);
            }
        }

        // Set charset
        $mysqli->set_charset($charset);

        // Create a simple PDO-like wrapper for mysqli
        class PDOCompat {
            private $mysqli;

            public function __construct($mysqli) {
                $this->mysqli = $mysqli;
            }

            public function query($sql) {
                $result = $this->mysqli->query($sql);
                if (!$result) {
                    die("Query failed: " . $this->mysqli->error);
                }

                return new PDOCompatStatement($result);
            }

            public function prepare($sql) {
                $stmt = $this->mysqli->prepare($sql);
                if (!$stmt) {
                    die("Prepare failed: " . $this->mysqli->error);
                }

                return new PDOCompatPreparedStatement($stmt);
            }

            public function lastInsertId() {
                return $this->mysqli->insert_id;
            }
        }

        class PDOCompatStatement {
            private $result;
            private $rows = [];

            public function __construct($result) {
                $this->result = $result;
                while ($row = $result->fetch_assoc()) {
                    $this->rows[] = $row;
                }
            }

            public function fetchAll() {
                return $this->rows;
            }

            public function fetch() {
                return !empty($this->rows) ? array_shift($this->rows) : false;
            }
        }

        class PDOCompatPreparedStatement {
            private $stmt;

            public function __construct($stmt) {
                $this->stmt = $stmt;
            }

            public function execute($params = []) {
                if (empty($params)) {
                    $result = $this->stmt->execute();
                    if (!$result) {
                        die("Execute failed: " . $this->stmt->error);
                    }
                    return $result;
                }

                // Bind parameters
                $types = '';
                $bindParams = [];

                foreach ($params as $param) {
                    if (is_int($param)) {
                        $types .= 'i';
                    } elseif (is_float($param)) {
                        $types .= 'd';
                    } else {
                        $types .= 's';
                    }
                    $bindParams[] = $param;
                }

                // Create array of references
                $bindRefs = [];
                $bindRefs[] = &$types;

                foreach ($bindParams as $key => $value) {
                    $bindRefs[] = &$bindParams[$key];
                }

                call_user_func_array([$this->stmt, 'bind_param'], $bindRefs);

                $result = $this->stmt->execute();
                if (!$result) {
                    die("Execute failed: " . $this->stmt->error);
                }

                return $result;
            }

            public function fetch() {
                $result = $this->stmt->get_result();
                if ($result) {
                    return $result->fetch_assoc();
                }
                return false;
            }

            public function fetchAll() {
                $result = $this->stmt->get_result();
                if ($result) {
                    return $result->fetch_all(MYSQLI_ASSOC);
                }
                return [];
            }
        }

        // Create PDO-compatible object
        $pdo = new PDOCompat($mysqli);
    } catch (Exception $e) {
        echo '<div style="background-color: #f8d7da; color: #721c24; padding: 20px; margin: 20px; border-radius: 5px; border: 1px solid #f5c6cb;">';
        echo '<h2>Database Connection Error</h2>';
        echo '<p>Could not connect to the database using mysqli.</p>';
        echo '<p>Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<p>Please check your database settings and make sure MySQL is running.</p>';
        echo '</div>';
        exit;
    }
}
?>