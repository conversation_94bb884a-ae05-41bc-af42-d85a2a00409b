<?php
// Quick Database Test
echo "<h1>Database Connection Test</h1>";

$host = 'localhost';
$user = 'root';
$pass = '';

echo "<p><strong>Testing with:</strong></p>";
echo "<p>Host: " . htmlspecialchars($host) . "</p>";
echo "<p>User: " . htmlspecialchars($user) . "</p>";
echo "<p>Pass: " . (empty($pass) ? '(empty)' : '(set)') . "</p>";

try {
    $mysqli = new mysqli($host, $user, $pass);
    if ($mysqli->connect_error) {
        echo "<p style='color: red;'>Connection failed: " . $mysqli->connect_error . "</p>";
    } else {
        echo "<p style='color: green;'>✓ Connection successful!</p>";
        $mysqli->close();
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Exception: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>PHP Info</h2>";
phpinfo();
?>
