<?php
// Database setup script
$host = 'localhost';
$user = 'root';
$pass = '';
$db = 'cost_calculator';

// Debug: Display current configuration
echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px; border: 1px solid #ccc;'>";
echo "<h3>Debug Info:</h3>";
echo "<p><strong>Host:</strong> " . htmlspecialchars($host) . "</p>";
echo "<p><strong>User:</strong> " . htmlspecialchars($user) . "</p>";
echo "<p><strong>Database:</strong> " . htmlspecialchars($db) . "</p>";
echo "<p><strong>File:</strong> " . __FILE__ . "</p>";
echo "<p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "</div>";

$title = "Database Setup";
$messages = [];
$errors = [];
$success = false;

// Function to add a message
function addMessage($message) {
    global $messages;
    $messages[] = $message;
}

// Function to add an error
function addError($message) {
    global $errors;
    $errors[] = $message;
}

// Try to connect to MySQL
try {
    // Connect to MySQL without selecting a database
    $mysqli = new mysqli($host, $user, $pass);

    // Check connection
    if ($mysqli->connect_error) {
        throw new Exception("Connection failed: " . $mysqli->connect_error);
    }

    addMessage("Connected to MySQL server successfully.");

    // Create database if it doesn't exist
    $result = $mysqli->query("CREATE DATABASE IF NOT EXISTS $db");
    if (!$result) {
        throw new Exception("Error creating database: " . $mysqli->error);
    }

    addMessage("Database '$db' created or already exists.");

    // Select the database
    $mysqli->select_db($db);
    addMessage("Selected database '$db'.");

    // Create tables
    $tables = [
        'users' => "CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'viewer') NOT NULL DEFAULT 'viewer',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",

        'categories' => "CREATE TABLE IF NOT EXISTS categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",

        'subcategories' => "CREATE TABLE IF NOT EXISTS subcategories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            category_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
        )",

        'costs' => "CREATE TABLE IF NOT EXISTS costs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            description VARCHAR(255) NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            category_id INT NOT NULL,
            subcategory_id INT NOT NULL,
            rate_type ENUM('daily', 'monthly') NOT NULL DEFAULT 'daily',
            num_days INT NOT NULL DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories(id),
            FOREIGN KEY (subcategory_id) REFERENCES subcategories(id)
        )",

        'revenue' => "CREATE TABLE IF NOT EXISTS revenue (
            id INT AUTO_INCREMENT PRIMARY KEY,
            month_year VARCHAR(7) NOT NULL,
            student_type VARCHAR(50) NOT NULL,
            revenue_per_student DECIMAL(10,2) NOT NULL,
            total_students INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",

        'invoices' => "CREATE TABLE IF NOT EXISTS invoices (
            id INT AUTO_INCREMENT PRIMARY KEY,
            student_name VARCHAR(100) NOT NULL,
            course VARCHAR(100) NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            tax DECIMAL(5,2) NOT NULL DEFAULT 0,
            due_date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )"
    ];

    // Create each table
    foreach ($tables as $table => $sql) {
        if ($mysqli->query($sql)) {
            addMessage("Table '$table' created or already exists.");
        } else {
            throw new Exception("Error creating table '$table': " . $mysqli->error);
        }
    }

    // Check if admin user exists
    $result = $mysqli->query("SELECT id FROM users WHERE username = 'admin'");
    if ($result->num_rows == 0) {
        // Create default admin user (username: admin, password: admin123)
        $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (username, password, role) VALUES ('admin', '$password_hash', 'admin')";

        if ($mysqli->query($sql)) {
            addMessage("Default admin user created. Username: admin, Password: admin123");
        } else {
            throw new Exception("Error creating admin user: " . $mysqli->error);
        }
    } else {
        addMessage("Admin user already exists.");
    }

    // Insert sample categories if none exist
    $result = $mysqli->query("SELECT id FROM categories LIMIT 1");
    if ($result->num_rows == 0) {
        $categories = [
            ['Salaries', 'Staff and instructor salaries'],
            ['Facilities', 'Building rent and maintenance'],
            ['Equipment', 'Learning equipment and supplies'],
            ['Marketing', 'Advertising and promotional activities']
        ];

        foreach ($categories as $cat) {
            $name = $mysqli->real_escape_string($cat[0]);
            $desc = $mysqli->real_escape_string($cat[1]);
            $sql = "INSERT INTO categories (name, description) VALUES ('$name', '$desc')";
            if (!$mysqli->query($sql)) {
                throw new Exception("Error adding category '$name': " . $mysqli->error);
            }
        }

        addMessage("Sample categories added.");

        // Add sample subcategories
        $categoryIds = [];
        $result = $mysqli->query("SELECT id, name FROM categories");
        while ($row = $result->fetch_assoc()) {
            $categoryIds[$row['name']] = $row['id'];
        }

        $subcategories = [
            ['Salaries', 'Full-time Staff', 'Permanent employees'],
            ['Salaries', 'Part-time Instructors', 'Hourly instructors'],
            ['Facilities', 'Rent', 'Monthly building rent'],
            ['Facilities', 'Utilities', 'Electricity, water, internet'],
            ['Equipment', 'Computers', 'Laptops and desktops'],
            ['Equipment', 'Software', 'Licensed software'],
            ['Marketing', 'Online Ads', 'Google and social media ads'],
            ['Marketing', 'Print Materials', 'Brochures and flyers']
        ];

        foreach ($subcategories as $sub) {
            $catName = $sub[0];
            $name = $mysqli->real_escape_string($sub[1]);
            $desc = $mysqli->real_escape_string($sub[2]);
            $catId = $categoryIds[$catName] ?? null;

            if ($catId) {
                $sql = "INSERT INTO subcategories (category_id, name, description) VALUES ($catId, '$name', '$desc')";
                if (!$mysqli->query($sql)) {
                    throw new Exception("Error adding subcategory '$name': " . $mysqli->error);
                }
            }
        }

        addMessage("Sample subcategories added.");
    } else {
        addMessage("Categories already exist.");
    }

    // Close connection
    $mysqli->close();

    $success = true;
    addMessage("Database setup completed successfully!");
} catch (Exception $e) {
    addError($e->getMessage());
    if (isset($mysqli)) {
        $mysqli->close();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $title; ?></title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .setup-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            background-color: var(--card);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }
        .message {
            padding: 15px;
            margin-bottom: 10px;
            border-radius: var(--border-radius);
        }
        .success-message {
            background-color: #d4edda;
            color: #155724;
            border-left: 5px solid #28a745;
        }
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            border-left: 5px solid #dc3545;
        }
        .info-message {
            background-color: #e2f0fb;
            color: #0c5460;
            border-left: 5px solid #17a2b8;
        }
        .setup-actions {
            margin-top: 30px;
            display: flex;
            gap: 10px;
        }
        .setup-actions a {
            padding: 10px 20px;
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: bold;
            text-align: center;
        }
        .primary-action {
            background-color: var(--primary);
            color: white;
        }
        .secondary-action {
            background-color: var(--secondary);
            color: white;
        }
        .setup-steps {
            margin: 20px 0;
            padding: 0;
            list-style-position: inside;
        }
        .setup-steps li {
            margin-bottom: 10px;
            padding-left: 20px;
            position: relative;
        }
        .setup-steps li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <h1><?php echo $title; ?></h1>

        <?php if ($success): ?>
            <div class="message success-message">
                <h2>Setup Completed Successfully!</h2>
                <p>Your database has been set up and is ready to use.</p>
            </div>

            <h3>Setup Summary:</h3>
            <ul class="setup-steps">
                <?php foreach ($messages as $message): ?>
                    <li><?php echo htmlspecialchars($message); ?></li>
                <?php endforeach; ?>
            </ul>

            <div class="setup-actions">
                <a href="auth/login.php" class="primary-action">Login to Your Account</a>
                <a href="index.php" class="secondary-action">Go to Homepage</a>
            </div>

            <div class="message info-message" style="margin-top: 20px;">
                <h3>Default Admin Account:</h3>
                <p><strong>Username:</strong> admin</p>
                <p><strong>Password:</strong> admin123</p>
                <p><em>Note: Please change this password after your first login for security reasons.</em></p>
            </div>

        <?php else: ?>
            <?php if (!empty($errors)): ?>
                <div class="message error-message">
                    <h2>Setup Failed</h2>
                    <p>The following errors occurred during setup:</p>
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>

                <h3>Troubleshooting Tips:</h3>
                <ul>
                    <li>Make sure your MySQL server is running</li>
                    <li>Check that the username and password are correct</li>
                    <li>Ensure you have permission to create databases</li>
                    <li>Try running the <a href="diagnose.php">diagnostic tool</a> for more information</li>
                </ul>

                <div class="setup-actions">
                    <a href="setup.php" class="primary-action">Try Again</a>
                    <a href="diagnose.php" class="secondary-action">Run Diagnostics</a>
                </div>
            <?php else: ?>
                <div class="message info-message">
                    <p>Setting up your database...</p>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</body>
</html>
