<?php
// Fresh Database setup script - bypassing any caching issues
$host = 'localhost';
$user = 'root';
$pass = '';
$db = 'cost_calculator';

echo "<h1>Fresh Database Setup</h1>";
echo "<div style='background: #e8f5e8; padding: 10px; margin: 10px; border: 1px solid #4CAF50;'>";
echo "<h3>Configuration Check:</h3>";
echo "<p><strong>Host:</strong> " . htmlspecialchars($host) . "</p>";
echo "<p><strong>User:</strong> " . htmlspecialchars($user) . "</p>";
echo "<p><strong>Database:</strong> " . htmlspecialchars($db) . "</p>";
echo "<p><strong>File:</strong> " . __FILE__ . "</p>";
echo "<p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "</div>";

$messages = [];
$errors = [];

// Function to add a message
function addMessage($message) {
    global $messages;
    $messages[] = $message;
    echo "<p style='color: green;'>✓ " . htmlspecialchars($message) . "</p>";
}

// Function to add an error
function addError($message) {
    global $errors;
    $errors[] = $message;
    echo "<p style='color: red;'>✗ " . htmlspecialchars($message) . "</p>";
}

echo "<h2>Starting Database Setup...</h2>";

// Try to connect to MySQL
try {
    echo "<p>Attempting to connect to MySQL server...</p>";
    
    // Connect to MySQL without selecting a database
    $mysqli = new mysqli($host, $user, $pass);

    // Check connection
    if ($mysqli->connect_error) {
        throw new Exception("Connection failed: " . $mysqli->connect_error);
    }

    addMessage("Connected to MySQL server successfully.");

    // Create database if it doesn't exist
    echo "<p>Creating database if it doesn't exist...</p>";
    $result = $mysqli->query("CREATE DATABASE IF NOT EXISTS $db");
    if (!$result) {
        throw new Exception("Error creating database: " . $mysqli->error);
    }

    addMessage("Database '$db' created or already exists.");

    // Select the database
    $mysqli->select_db($db);
    addMessage("Selected database '$db'.");

    // Create users table
    echo "<p>Creating users table...</p>";
    $sql = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        role ENUM('admin', 'viewer') NOT NULL DEFAULT 'viewer',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($mysqli->query($sql)) {
        addMessage("Users table created or already exists.");
    } else {
        throw new Exception("Error creating users table: " . $mysqli->error);
    }

    // Create categories table
    echo "<p>Creating categories table...</p>";
    $sql = "CREATE TABLE IF NOT EXISTS categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($mysqli->query($sql)) {
        addMessage("Categories table created or already exists.");
    } else {
        throw new Exception("Error creating categories table: " . $mysqli->error);
    }

    // Check if admin user exists
    echo "<p>Checking for admin user...</p>";
    $result = $mysqli->query("SELECT id FROM users WHERE username = 'admin'");
    if ($result->num_rows == 0) {
        // Create default admin user
        $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (username, password, role) VALUES ('admin', '$password_hash', 'admin')";

        if ($mysqli->query($sql)) {
            addMessage("Default admin user created. Username: admin, Password: admin123");
        } else {
            throw new Exception("Error creating admin user: " . $mysqli->error);
        }
    } else {
        addMessage("Admin user already exists.");
    }

    // Close connection
    $mysqli->close();

    echo "<div style='background: #d4edda; color: #155724; padding: 20px; margin: 20px; border-radius: 5px;'>";
    echo "<h2>✓ Setup Completed Successfully!</h2>";
    echo "<p>Your database has been set up and is ready to use.</p>";
    echo "<p><a href='auth/login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Login to Your Account</a></p>";
    echo "</div>";

} catch (Exception $e) {
    addError($e->getMessage());
    
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; margin: 20px; border-radius: 5px;'>";
    echo "<h2>Setup Failed</h2>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<h3>Troubleshooting:</h3>";
    echo "<ul>";
    echo "<li>Make sure XAMPP/WAMP is running</li>";
    echo "<li>Make sure MySQL service is started</li>";
    echo "<li>Check that the username and password are correct</li>";
    echo "</ul>";
    echo "</div>";
    
    if (isset($mysqli)) {
        $mysqli->close();
    }
}
?>
