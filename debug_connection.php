<?php
echo "<h1>Database Connection Debug</h1>";

// Check PHP extensions
echo "<h2>PHP Extensions Check</h2>";
echo "<p>PDO MySQL: " . (extension_loaded('pdo_mysql') ? '✓ Available' : '✗ Not Available') . "</p>";
echo "<p>MySQLi: " . (extension_loaded('mysqli') ? '✓ Available' : '✗ Not Available') . "</p>";

// Test different connection methods
$host = 'localhost';
$user = 'root';
$pass = '';
$db = 'cost_calculator';

echo "<h2>Connection Variables</h2>";
echo "<p><strong>Host:</strong> " . htmlspecialchars($host) . "</p>";
echo "<p><strong>User:</strong> " . htmlspecialchars($user) . "</p>";
echo "<p><strong>Database:</strong> " . htmlspecialchars($db) . "</p>";

echo "<h2>MySQLi Connection Test</h2>";
try {
    echo "<p>Attempting MySQLi connection...</p>";
    $mysqli = new mysqli($host, $user, $pass);
    
    if ($mysqli->connect_error) {
        echo "<p style='color: red;'>MySQLi Error: " . $mysqli->connect_error . "</p>";
    } else {
        echo "<p style='color: green;'>✓ MySQLi connection successful!</p>";
        
        // Test database creation
        $result = $mysqli->query("CREATE DATABASE IF NOT EXISTS test_db");
        if ($result) {
            echo "<p style='color: green;'>✓ Can create databases</p>";
            $mysqli->query("DROP DATABASE IF EXISTS test_db");
        } else {
            echo "<p style='color: orange;'>⚠ Cannot create databases: " . $mysqli->error . "</p>";
        }
        
        $mysqli->close();
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>MySQLi Exception: " . $e->getMessage() . "</p>";
}

echo "<h2>PDO Connection Test</h2>";
if (extension_loaded('pdo_mysql')) {
    try {
        echo "<p>Attempting PDO connection...</p>";
        $pdo = new PDO("mysql:host=$host", $user, $pass);
        echo "<p style='color: green;'>✓ PDO connection successful!</p>";
    } catch (PDOException $e) {
        echo "<p style='color: red;'>PDO Error: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: orange;'>PDO MySQL extension not available</p>";
}

echo "<h2>System Information</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Operating System:</strong> " . php_uname() . "</p>";
echo "<p><strong>Server Software:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
echo "<p><strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</p>";
echo "<p><strong>Script Path:</strong> " . __FILE__ . "</p>";

echo "<h2>Environment Variables</h2>";
$env_vars = ['DB_HOST', 'DATABASE_URL', 'MYSQL_HOST'];
foreach ($env_vars as $var) {
    $value = getenv($var);
    if ($value !== false) {
        echo "<p><strong>$var:</strong> " . htmlspecialchars($value) . "</p>";
    }
}

echo "<h2>Include Path</h2>";
echo "<p>" . get_include_path() . "</p>";

echo "<h2>Loaded Configuration Files</h2>";
$files = php_ini_loaded_file();
if ($files) {
    echo "<p>Main: " . $files . "</p>";
}
$additional = php_ini_scanned_files();
if ($additional) {
    echo "<p>Additional: " . $additional . "</p>";
}
?>
